variable "region" {
  description = "AWS region to host your network"
  default     = "us-east-1"
}
variable "profile" {}
variable "default_tags" {
  type = map(string)

  default = {
    section     = ""
    environment = ""
    longcode    = ""
    managedby   = "essilor"
	application = ""
  }
}

variable "name" {}
variable "cidr" {}
variable "azs" {
  type = list
}
variable "dev_private_subnets" {
  type = list
}
variable "dev_public_subnets" {
  type = list
}

variable "dev_inbound_acl_rules" {
  type = list
}
variable "dev_outbound_acl_rules" {
  type = list
}

variable "transit_subnets" {
  description = "Transit subnet CIDRs"
  type        = list(string)
  default     = ["*************/28", "*************/28"]
}

variable "transit_routes" {
  description = "Routes for Transit Subnet"
  type        = list(string)
  default     = ["10.0.0.0/8", "**********/12", "***********/16"]
}


data "aws_iam_account_alias" "current" {}