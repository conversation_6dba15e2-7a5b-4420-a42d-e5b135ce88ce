# output "vpc_id" {
#     description = "id of the vpc managed"
#     value = module.vpc.vpc_id
# }

# output "vpc_cidr" {
#     description = "cidr of the vpc"
#     value = var.cidr
# }

# output "storage_gateway_sg_id" {
#     description = "id of the security group affected to the storage gateway endpoint"
#     value = module.vpc.vpc_endpoint_storagegateway_security_group
# }

# output "storage_gateway_endpoint" {
#     description = "endpoint to access to the storage gateway service"
#     value = module.vpc.vpc_endpoint_storagegateway_dns_entry
# }

# output "api_gateway_sg_id" {
#     description = "id of the security group affected to the storage gateway endpoint"
#     value = module.vpc.vpc_endpoint_apigw_security_group
# }

# output "api_gateway_endpoint_id" {
#     description = "endpoint to access to the storage gateway service"
#     value = module.vpc.vpc_endpoint_apigw_id
# }