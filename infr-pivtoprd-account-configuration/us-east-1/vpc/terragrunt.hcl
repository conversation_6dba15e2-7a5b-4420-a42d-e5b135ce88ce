terraform{
    
}

include {
    path = find_in_parent_folders()
}

inputs = {
  name = "prd"

  cidr = "10.177.0.0/24"
  azs = ["us-east-1a", "us-east-1b"]
  dev_private_subnets = ["10.177.0.0/26","10.177.0.64/26","10.177.0.192/28","10.177.0.208/28"]
  dev_public_subnets = ["10.177.0.128/27","10.177.0.160/27"]
  transit_subnets = ["10.177.0.224/28", "10.177.0.240/28"]
  transit_routes = ["10.0.0.0/8", "172.16.0.0/12", "192.168.0.0/16"]
  dev_inbound_acl_rules = [
      {
        rule_number = 100
        rule_action = "allow"
        from_port   = 0
        to_port     = 0
        protocol    = "-1"
        cidr_block  = "0.0.0.0/0"
      }
  ]
  dev_outbound_acl_rules = [    
      {
        rule_number = 100
        rule_action = "allow"
        from_port   = 0
        to_port     = 0
        protocol    = "-1"
        cidr_block  = "0.0.0.0/0"
      }
  ]  
}
