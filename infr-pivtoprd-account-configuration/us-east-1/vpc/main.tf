module "vpc" {
  #source = "git::https://dev.azure.com/EL-RX-DigitalServices/CCOE%20Project/_git/infr-account-modules//module-vpc?ref=vpc-1.0.4"
  source = "../../../infr-account-modules/module-vpc"

  name = "${data.aws_iam_account_alias.current.account_alias}-${replace(var.region, "-", "")}-vpc-${var.name}"

  cidr = var.cidr
  one_nat_gateway_per_az = true
  azs                 = var.azs
  dev_private_subnets     = var.dev_private_subnets
  dev_public_subnets      = var.dev_public_subnets
  transit_subnets = var.transit_subnets
  transit_routes = var.transit_routes

  create_database_subnet_group = false

  enable_dns_hostnames = true
  enable_dns_support   = true

  enable_nat_gateway = true
  single_nat_gateway = false
  nat_gateway_hosted_on = "dev"

  enable_vpn_gateway = false
  enable_vpn_connection = false
  associate_vpn_gateway = false
  enable_sci_gateway = false
  associate_sci_gateway = false
  #dx_gateway_proposal = var.dx_gateway_association_proposal
  #dx_gateway_proposal = var.dx_gateway_association_proposal
  propagate_private_route_tables_vgw = false
  propagate_public_route_tables_vgw = false
  add_default_static_routes         = true

  # VPC endpoint for S3
  enable_s3_endpoint = false

  # VPC endpoint for DynamoDB
  enable_dynamodb_endpoint = false

  # VPC endpoint for Storage Gateway
  enable_storagegateway_endpoint = false
  storagegateway_endpoint_private_dns_enabled = false
  
  # VPC endpoint for api gateway
  enable_apigw_endpoint = false

  # NACL
  dev_inbound_acl_rules = var.dev_inbound_acl_rules
  dev_outbound_acl_rules = var.dev_outbound_acl_rules
  tags = var.default_tags
  
  enable_dhcp_options              = true
  dhcp_options_domain_name         = "service.essilor"
  dhcp_options_domain_name_servers = ["AmazonProvidedDNS"]  
}



data "aws_vpc" "selected_vpc" {
  id = module.vpc.vpc_id
}