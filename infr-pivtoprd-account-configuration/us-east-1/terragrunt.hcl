terraform_binary="terraform"
iam_role="arn:aws:iam::************:role/terraform"

remote_state {
    backend = "s3"
    config = {
        bucket          = "infr-euwest1-ccoe-terraform-states"
        key             = "infr-pivtoprd-account-config/us-east-1/${path_relative_to_include()}/terraform.tfstate"
        region          = "eu-west-1"
        role_arn        = "arn:aws:iam::************:role/terraform_backend"
        encrypt         = true
    }
}

locals {
    default_yaml_path = find_in_parent_folders("empty.yaml")
}
# remove ${get_terragrunt_dir()}/ maybe because of version change
inputs = merge(
    yamldecode(
        file("${find_in_parent_folders("environment.yaml", local.default_yaml_path)}"),
    ),
    yamldecode(
        file("${find_in_parent_folders("region.yaml", local.default_yaml_path)}"),
    ),
    {
        profile =   "terraform-pivtoprd"
    },
    yamldecode(
        file("${find_in_parent_folders("commons.yaml", local.default_yaml_path)}")
    ),
)
