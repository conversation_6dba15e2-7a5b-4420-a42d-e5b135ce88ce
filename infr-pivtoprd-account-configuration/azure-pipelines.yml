# Starter pipeline
# Start with a minimal pipeline that you can customize to build and deploy your code.
# Add steps that build, run tests, deploy, and more:
# https://aka.ms/yaml

trigger: none

pool: CCOE

resources:
  repositories:
    - repository: terraform-pipelines
      type: git
      name: terraform-pipelines

extends:
  template: azure-pipelines.yml@terraform-pipelines  # Template reference
  parameters:
    REPO_NAME: $(REPO_NAME)
    TERRAFORM_PROJECT_DIR: $(TERRAFORM_PROJECT_DIR)
