terraform{

}

dependency "kms_sessionmanager" {
    config_path = "../../kms"
}

include {
    path = find_in_parent_folders()
}

inputs = {
  session_manager_kms_id = dependency.kms_sessionmanager.outputs.kms_key_id

  prometheus_arns = ["arn:aws:iam::063324892558:root", "arn:aws:iam::210492164002:root"]

  kms_keys = ["arn:aws:kms:eu-west-1:063324892558:key/mrk-caacc6e435fd46be8b174da3074c1ff8","arn:aws:kms:ca-central-1:063324892558:key/mrk-caacc6e435fd46be8b174da3074c1ff8", "arn:aws:kms:eu-west-1:063324892558:key/45521a3f-bed6-4a77-b9a6-e6cc0228a6c2", "arn:aws:kms:eu-west-1:210492164002:key/6eb7a832-689a-4a64-b228-1581aa92aed8"]
}
